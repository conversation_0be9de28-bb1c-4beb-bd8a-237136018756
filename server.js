require('dotenv').config();
const { ApolloServer } = require('@apollo/server');
const { expressMiddleware } = require('@apollo/server/express4');
const { gql } = require('graphql-tag');
const { Neo4jGraphQL } = require('@neo4j/graphql');
const neo4j = require('neo4j-driver');
const { readFileSync } = require('fs');
const jwt = require('jsonwebtoken');
const express = require('express');
const cors = require('cors');

// Load schema and resolvers
const typeDefs = gql(readFileSync('./schema.gql', 'utf8'));
const { resolvers, JWT_SECRET: sharedJwtSecret } = require('./resolvers');

// Load Environment Variables
const {
  NEO4J_URI,
  NEO4J_DATABASE,
  NEO4J_USER,
  NEO4J_PASSWORD,
  PORT,
  CORS_ORIGINS,
  NODE_ENV
} = process.env;

// Validate required environment variables
const requiredEnvVars = {
  NEO4J_URI,
  NEO4J_DATABASE,
  NEO4J_USER,
  NEO4J_PASSWORD,
  PORT,
  CORS_ORIGINS
};

Object.entries(requiredEnvVars).forEach(([key, value]) => {
  if (!value) {
    console.error(`[ERROR] Missing required environment variable: ${key}`);
    process.exit(1);
  }
});

// Driver Auth and Timeout
const driver = neo4j.driver(
  NEO4J_URI,
  neo4j.auth.basic(NEO4J_USER, NEO4J_PASSWORD),
  { 
    database: NEO4J_DATABASE,
    connectionTimeout: 20000
  }
); 

// Bundles Neo Components
const hiveMapBundle = new Neo4jGraphQL({
  typeDefs,
  driver,
  resolvers,
  features: {
    authorization: {
      key: sharedJwtSecret,
      verify: true,
      verifyOptions: {
        algorithms: ['HS256']
      }
    }
  }
});

// Verifies DB with Pulse
async function requestHivePulse() {
  let session;
  try {
    session = driver.session();
    await session.run('RETURN 1992');
    console.log('[PASS] Hive Responded to Pulse.');
  } catch (error) {
    console.error('[ERROR] Hive Unresponsive to Pulse.');
    process.exit(1);
  } finally {
    if (session) {
      await session.close();
    }
  }
}

// Generates Hive Map / Schema
async function generateHiveMap() {
  const hiveMap = await hiveMapBundle.getSchema();
  console.log('[PASS] Hive Map Ready.');
  return hiveMap;
}

// Starts API Server with Schema
async function setupApiServer(schema) {
  const server = new ApolloServer({
    schema,
    introspection: true,
  });
  await server.start();
  console.log('[PASS] API Server Ready.');
  return server;
}

// Primary Server Process
async function startServer() {
  console.log('[INFO] Pulsing Hive...');                   
  console.log('------');

  await requestHivePulse();
  const hiveMap = await generateHiveMap();
  const server = await setupApiServer(hiveMap);
  const app = express();

  // Parse CORS origins from environment variable
  const allowedOrigins = CORS_ORIGINS.split(',').map(origin => origin.trim());

  app.use(cors({
    origin: allowedOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
  }));
  console.log('[PASS] API CORS Configured.');

  app.use('/graphql',
    express.json(),
    expressMiddleware(server, {
      context: async ({ req }) => {
        const token = req.headers.authorization;
        return { 
          token,
          driver 
        };
      },
      bodyParserConfig: { limit: '10mb' }
    })
  );

  console.log('[PASS] API Middleware Configured.');
  console.log('------');

  app.listen(PORT, () => {
    console.log(`[HIVE] Online!`);
    console.log(`[HIVE] Listening to ${PORT}/graphql`);
  });
}

// Catch Errors
startServer().catch(err => {
  console.error('[ERROR] Failed to start Hive:', err);
  process.exit(1);
});