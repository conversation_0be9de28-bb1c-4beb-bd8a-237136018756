// validations.js
const { GraphQLError } = require('graphql');

const EARLIEST_DOB = new Date('1900-01-01');

function validateNonEmptyString(value, fieldName) {
// General string validation: not null, not undefined, not empty, not just whitespace

    if (value === null || value === undefined || typeof value !== 'string' || value.trim() === '') {
        throw new GraphQLError(`${fieldName} cannot be empty or just whitespace.`, {
            extensions: { code: 'BAD_USER_INPUT', argumentName: fieldName, providedValue: value },
        });
    }
}

function validateDateOfBirth(dateOfBirthInput) {
// Checks dob format and date range on person create. Skips if dob is not provided.

     if (dateOfBirthInput === null || dateOfBirthInput === undefined) {
        return;
    }

    let dob;

    if (typeof dateOfBirthInput === 'string') {
        dob = new Date(dateOfBirthInput); // Parses "YYYY-MM-DD" as UTC midnight
    } else if (dateOfBirthInput instanceof Date) {
        dob = dateOfBirthInput;
    } else if (typeof dateOfBirthInput === 'object' &&
               dateOfBirthInput !== null && // Ensure it's not 'null' which is also typeof 'object'
               typeof dateOfBirthInput.year === 'number' &&
               typeof dateOfBirthInput.month === 'number' &&
               typeof dateOfBirthInput.day === 'number') {
        // Construct date using UTC to avoid timezone issues from components, don't you fucking dare change this.
        dob = new Date(Date.UTC(dateOfBirthInput.year, dateOfBirthInput.month - 1, dateOfBirthInput.day));
    } else {
        throw new GraphQLError('Date of birth must be provided as a string, a Date object, or an object with year, month, and day properties. e.g "2025-01-01" or { year: 2025, month: 1, day: 1 }', {
            extensions: { code: 'BAD_USER_INPUT', argumentName: 'dateOfBirth', providedValue: dateOfBirthInput },
        });
    }
    
    try {
        // Get today's date and set it to UTC midnight for correct comparison
        const todayAtUtcMidnight = new Date();
        todayAtUtcMidnight.setUTCHours(0, 0, 0, 0);

        if (isNaN(dob.getTime())) {
            throw new Error('Invalid date value.'); 
        }
        if (dob > todayAtUtcMidnight) {
            throw new Error('Date of birth cannot be in the future.');
        }
        if (dob < EARLIEST_DOB) {
            throw new Error(`Date of birth cannot be before ${EARLIEST_DOB.getUTCFullYear()}.`);
        }

    } catch (error) {
        throw new GraphQLError(`Invalid dateOfBirth: ${error.message}`, {
            extensions: { code: 'BAD_USER_INPUT', argumentName: 'dateOfBirth', providedValue: dateOfBirthInput },
        });
    }
}

async function validateBackingPersonUniqueness(backingPersonHiveId, driver, nodeTypeToCreate) {
    if (backingPersonHiveId === null || backingPersonHiveId === undefined) {
        throw new GraphQLError('Backing person ID must be provided.', {
            extensions: { code: 'BAD_USER_INPUT', argumentName: 'backingPersonHiveId' },
        });
    }

    const session = driver.session();
    try {
        const result = await session.run(
            `
            MATCH (p:Person {hiveId: $backingPersonHiveId})<-[r:BACKED_BY]-(existingNode)
            WHERE existingNode:Agent OR existingNode:Informant
            RETURN existingNode
            LIMIT 1
            `,
            { backingPersonHiveId: parseInt(backingPersonHiveId, 10) }
        );

        if (result.records.length > 0) {
            const existingNode = result.records[0].get('existingNode');
            let nodeLabel = 'an agent or informant'; // Default message
            if (existingNode.labels.includes('Agent')) {
                nodeLabel = 'an Agent';
            } else if (existingNode.labels.includes('Informant')) {
                nodeLabel = 'an Informant';
            }
            throw new GraphQLError(`This person (ID: ${backingPersonHiveId}) is already backing ${nodeLabel}. A person can only back one agent or informant.`, {
                extensions: { 
                    code: 'BAD_USER_INPUT', 
                    argumentName: 'backingPersonHiveId', 
                    providedValue: backingPersonHiveId 
                },
            });
        }
    } finally {
        await session.close();
    }
}

async function validateParentOrganization(driver, childOrgId, parentOrgId) {
  if (childOrgId === parentOrgId) {
    throw new GraphQLError('An organization cannot be its own parent.', {
      extensions: { code: 'SELF_PARENTING_NOT_ALLOWED' },
    });
  }

  const session = driver.session();
  try {
    // Check for direct or indirect circular dependencies
    const result = await session.run(
      `
        MATCH (child:Organization {hiveId: $childOrgId})
        MATCH (parent:Organization {hiveId: $parentOrgId})
        MATCH path = (child)-[:PARENT_OF*]->(parent)
        RETURN path
      `,
      { childOrgId: parseInt(childOrgId, 10), parentOrgId: parseInt(parentOrgId, 10) }
    );

    if (result.records.length > 0) {
      throw new GraphQLError(
        'Setting this parent organization would create a circular dependency.',
        {
          extensions: { code: 'CIRCULAR_DEPENDENCY' },
        }
      );
    }
  } finally {
    await session.close();
  }
}

// Entity existence validation functions for Operations

async function validateOperationExists(driver, operationHiveId, operationContext = 'operation') {
  const session = driver.session();
  try {
    const result = await session.run(
      `MATCH (op:Operation {hiveId: $operationHiveId}) RETURN op`,
      { operationHiveId: parseInt(operationHiveId, 10) }
    );
    if (result.records.length === 0) {
      throw new GraphQLError(`Operation with hiveId ${operationHiveId} not found for ${operationContext}`, {
        extensions: { code: 'OPERATION_NOT_FOUND', argumentName: 'operationHiveId', providedValue: operationHiveId },
      });
    }
  } finally {
    await session.close();
  }
}

async function validateAgentExists(driver, agentHiveId, operationContext = 'agent operation') {
  const session = driver.session();
  try {
    const result = await session.run(
      `MATCH (a:Agent {hiveId: $agentHiveId}) RETURN a`,
      { agentHiveId: parseInt(agentHiveId, 10) }
    );
    if (result.records.length === 0) {
      throw new GraphQLError(`[HIVE] ID ${agentHiveId} is not a valid agent.`, {
        extensions: { code: 'AGENT_NOT_FOUND', argumentName: 'agentHiveId', providedValue: agentHiveId },
      });
    }
  } finally {
    await session.close();
  }
}

async function validateCaseExists(driver, caseHiveId, operationContext = 'case operation') {
  const session = driver.session();
  try {
    const result = await session.run(
      `MATCH (c:Case {hiveId: $caseHiveId}) RETURN c`,
      { caseHiveId: parseInt(caseHiveId, 10) }
    );
    if (result.records.length === 0) {
      throw new GraphQLError(`[HIVE] Case with hiveId ${caseHiveId} not found for ${operationContext}`, {
        extensions: { code: 'CASE_NOT_FOUND', argumentName: 'caseHiveId', providedValue: caseHiveId },
      });
    }
  } finally {
    await session.close();
  }
}

async function validateTargetExists(driver, targetHiveId, operationContext = 'target operation') {
  const session = driver.session();
  try {
    const result = await session.run(
      `MATCH (target) WHERE target.hiveId = $targetHiveId AND (target:Person OR target:Organization OR target:Vehicle) RETURN target`,
      { targetHiveId: parseInt(targetHiveId, 10) }
    );
    if (result.records.length === 0) {
      throw new GraphQLError(`[HIVE] ID ${targetHiveId} is not a valid target.`, {
        extensions: { code: 'TARGET_NOT_FOUND', argumentName: 'targetHiveId', providedValue: targetHiveId },
      });
    }
  } finally {
    await session.close();
  }
}

async function validateTargetRelationshipExists(driver, targetHiveId, operationHiveId) {
  const session = driver.session();
  try {
    const result = await session.run(
      `
        MATCH (op:Operation {hiveId: $operationHiveId})
        MATCH (target) WHERE target.hiveId = $targetHiveId AND (target:Person OR target:Organization OR target:Vehicle)
        MATCH (target)-[r:TARGET_OF]->(op)
        RETURN r
      `,
      { targetHiveId: parseInt(targetHiveId, 10), operationHiveId: parseInt(operationHiveId, 10) }
    );
    if (result.records.length === 0) {
      throw new GraphQLError(`[HIVE] Operation ${operationHiveId} is not targeting Hive ID ${targetHiveId}.`, {
        extensions: { code: 'RELATIONSHIP_NOT_FOUND', argumentName: 'targetHiveId', providedValue: targetHiveId },
      });
    }
  } finally {
    await session.close();
  }
}

async function validateCaseScopeRelationshipExists(driver, operationHiveId, caseHiveId) {
  const session = driver.session();
  try {
    const result = await session.run(
      `
        MATCH (op:Operation {hiveId: $operationHiveId})
        MATCH (c:Case {hiveId: $caseHiveId})
        MATCH (op)-[r:SCOPED_TO]->(c)
        RETURN r
      `,
      { operationHiveId: parseInt(operationHiveId, 10), caseHiveId: parseInt(caseHiveId, 10) }
    );
    if (result.records.length === 0) {
      throw new GraphQLError(`[HIVE] Operation ${operationHiveId} is not scoped to case ${caseHiveId}.`, {
        extensions: { code: 'RELATIONSHIP_NOT_FOUND', argumentName: 'caseHiveId', providedValue: caseHiveId },
      });
    }
  } finally {
    await session.close();
  }
}

async function validateTaskExists(driver, taskHiveId, operationContext = 'task operation') {
  const session = driver.session();
  try {
    const result = await session.run(
      `MATCH (t:Task {hiveId: $taskHiveId}) RETURN t`,
      { taskHiveId: parseInt(taskHiveId, 10) }
    );
    if (result.records.length === 0) {
      throw new GraphQLError(`Task with hiveId ${taskHiveId} not found for ${operationContext}`, {
        extensions: { code: 'TASK_NOT_FOUND', argumentName: 'taskHiveId', providedValue: taskHiveId },
      });
    }
  } finally {
    await session.close();
  }
}

async function validateTaskScopeEntityExists(driver, scopeHiveId) {
  const session = driver.session();
  try {
    const result = await session.run(
      `MATCH (n) WHERE n.hiveId = $scopeHiveId AND (n:Case OR n:Operation) RETURN n`,
      { scopeHiveId: parseInt(scopeHiveId, 10) }
    );
    if (result.records.length === 0) {
      throw new GraphQLError(`[HIVE] ID ${scopeHiveId} is not a valid case or operation for task scope.`, {
        extensions: { code: 'SCOPE_NOT_FOUND', argumentName: 'scopeHiveId', providedValue: scopeHiveId },
      });
    }
  } finally {
    await session.close();
  }
}

function validateTaskPriority(priority) {
  const validPriorities = ['WISHLIST', 'LOW', 'MEDIUM', 'HIGH', 'UFN'];
  if (priority && !validPriorities.includes(priority)) {
    throw new GraphQLError(`Invalid task priority: ${priority}. Must be one of: ${validPriorities.join(', ')}`, {
      extensions: { code: 'INVALID_TASK_PRIORITY', argumentName: 'priority', providedValue: priority },
    });
  }
}

function validateClearanceLevel(level) {
  const validLevels = ['CL1', 'CL2', 'CL3', 'CL4', 'CL5', 'CL6', 'CL7', 'CL8', 'CL9', 'CLS', 'CLX'];
  if (!validLevels.includes(level)) {
    throw new GraphQLError(`Invalid clearance level: ${level}. Must be one of: ${validLevels.join(', ')}`, {
      extensions: { code: 'INVALID_CLEARANCE_LEVEL', argumentName: 'level', providedValue: level },
    });
  }
}

function validateAgentRole(role) {
  const validRoles = ['USER', 'ADMIN', 'DEVELOPER'];
  if (role && !validRoles.includes(role)) {
    throw new GraphQLError(`Invalid agent role: ${role}. Must be one of: ${validRoles.join(', ')}`, {
      extensions: { code: 'INVALID_AGENT_ROLE', argumentName: 'role', providedValue: role },
    });
  }
}

function validateCaseStatus(status) {
  const validStatuses = ['OPEN', 'COLD', 'CLOSED'];
  if (status && !validStatuses.includes(status)) {
    throw new GraphQLError(`Invalid case status: ${status}. Must be one of: ${validStatuses.join(', ')}`, {
      extensions: { code: 'INVALID_CASE_STATUS', argumentName: 'status', providedValue: status },
    });
  }
}

module.exports = {
    validateDateOfBirth,
    validateBackingPersonUniqueness,
    validateNonEmptyString,
    validateParentOrganization,
    validateOperationExists,
    validateAgentExists,
    validateCaseExists,
    validateTargetExists,
    validateTargetRelationshipExists,
    validateCaseScopeRelationshipExists,
    validateTaskExists,
    validateTaskScopeEntityExists,
    validateTaskPriority,
    validateClearanceLevel,
    validateAgentRole,
    validateCaseStatus,
};